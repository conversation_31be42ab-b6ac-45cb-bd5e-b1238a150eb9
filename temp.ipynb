import cv2
import numpy as np
import matplotlib.pyplot as plt

import cv2
import numpy as np
import matplotlib.pyplot as plt

# Read image
img = cv2.imread('img1.png')
img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # convert to RGB for matplotlib

# Initialize mask and models
mask = np.zeros(img.shape[:2], np.uint8)
bgdModel = np.zeros((1, 65), np.float64)
fgdModel = np.zeros((1, 65), np.float64)

# Define rectangle for GrabCut
rect = (10, 10, img.shape[1]-10, img.shape[0]-10)

# Apply GrabCut
cv2.grabCut(img, mask, rect, bgdModel, fgdModel, 5, cv2.GC_INIT_WITH_RECT)

# Extract masks
mask2 = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')

# Separate foreground and background
foreground = img * mask2[:, :, np.newaxis]
background = img * (1 - mask2[:, :, np.newaxis])

# Plot
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.imshow(img)
plt.title('Original Image')
plt.axis('off')

plt.subplot(1, 3, 2)
plt.imshow(foreground)
plt.title('Foreground (Person)')
plt.axis('off')

plt.subplot(1, 3, 3)
plt.imshow(background)
plt.title('Background')
plt.axis('off')

plt.tight_layout()
plt.show()

import cv2
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans

# Read image
img = cv2.imread('img1.png')
img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
h, w = img.shape[:2]

# Border thickness
b = 10

# ---- ✅ Extract border pixels properly ----
top = img[0:b, :, :].reshape(-1, 3)
bottom = img[h-b:h, :, :].reshape(-1, 3)
left = img[:, 0:b, :].reshape(-1, 3)
right = img[:, w-b:w, :].reshape(-1, 3)

# Concatenate flattened borders safely
border = np.concatenate([top, bottom, left, right], axis=0)

# ---- Cluster to find dominant background color ----
kmeans = KMeans(n_clusters=2, random_state=0).fit(border)
bg_color = kmeans.cluster_centers_[np.argmax(np.bincount(kmeans.labels_))]

# ---- Compute color distance map ----
dist = np.linalg.norm(img - bg_color, axis=2)
dist_norm = (dist - dist.min()) / (dist.max() - dist.min())

# ---- Build GrabCut mask ----
mask = np.zeros((h, w), np.uint8)
mask[dist_norm < 0.25] = cv2.GC_BGD
mask[dist_norm > 0.6] = cv2.GC_FGD
mask[(dist_norm >= 0.25) & (dist_norm <= 0.6)] = cv2.GC_PR_FGD

# ---- Run GrabCut ----
bgdModel = np.zeros((1, 65), np.float64)
fgdModel = np.zeros((1, 65), np.float64)
cv2.grabCut(img_rgb, mask, None, bgdModel, fgdModel, 5, cv2.GC_INIT_WITH_MASK)

# ---- Extract foreground/background ----
mask2 = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
foreground = img_rgb * mask2[:, :, np.newaxis]
background = img_rgb * (1 - mask2[:, :, np.newaxis])

# ---- Plot results ----
plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1); plt.imshow(img_rgb); plt.title('Original'); plt.axis('off')
plt.subplot(1, 3, 2); plt.imshow(foreground); plt.title('Foreground'); plt.axis('off')
plt.subplot(1, 3, 3); plt.imshow(background); plt.title('Background'); plt.axis('off')
plt.tight_layout()
plt.show()
