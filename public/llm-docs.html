<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Documentation - Guj-Eng OCR-Free Image Translation</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/docs.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><i class="fas fa-language"></i> Guj-Eng Translator</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="llm-docs.html" class="nav-link active">LLM Docs</a>
                <a href="technical-docs.html" class="nav-link">Technical</a>
                <a href="https://github.com/patelchaitany/Guj_eng" class="nav-link github-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </div>
            <div class="hamburger" id="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main class="docs-main">
        <div class="container">
            <div class="docs-header">
                <h1><i class="fas fa-robot"></i> LLM-Friendly Documentation</h1>
                <p>Structured information for Large Language Model consumption and understanding</p>
            </div>

            <div class="docs-content">
                <section class="doc-section">
                    <h2>PROJECT_OVERVIEW</h2>
                    <div class="structured-data">
                        <pre><code class="language-yaml">project_name: "End-to-End OCR-Free Image Translation for Low-Resource Languages"
primary_language_pair: "Gujarati ↔ English"
approach: "Direct image-to-text translation without OCR preprocessing"
target_domain: "Low-resource language translation"
key_innovation: "Vision Transformer integration for OCR-free translation"

problem_statement: |
  Traditional image translation pipelines rely on OCR followed by machine translation,
  which introduces error propagation and fails in low-resource settings where OCR
  quality is poor or unavailable.

solution_approach: |
  End-to-end neural architecture that directly translates text in images to target
  language without intermediate OCR step, specifically adapted for Gujarati-English
  translation using Vision Transformers.

research_foundation:
  paper: "Improving End-to-End Text Image Translation From the Auxiliary Text Translation Task"
  authors: "Ma, Cong et al."
  arxiv_id: "2210.03887"
  year: 2022
  adaptation: "Extended to Gujarati with ViT encoder replacement"</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>TECHNICAL_ARCHITECTURE</h2>
                    <div class="structured-data">
                        <pre><code class="language-yaml">model_architecture:
  type: "Encoder-Decoder Transformer"
  image_encoder:
    type: "CLIP Vision Encoder"
    input_size: [224, 224, 3]
    output_dim: 768
    purpose: "Extract visual features from text images"
  
  text_encoder:
    type: "Transformer Encoder"
    layers: 6
    hidden_dim: 768
    attention_heads: 8
    feedforward_dim: 768
    
  text_decoder:
    type: "Transformer Decoder with Cross-Attention"
    layers: 6
    hidden_dim: 768
    attention_heads: 8
    feedforward_dim: 768
    
  tokenizer:
    type: "SentencePiece BPE"
    vocab_size: "Dynamic based on training data"
    special_tokens: ["&lt;en|gu&gt;", "&lt;gu|en&gt;", "&lt;en&gt;", "&lt;gu&gt;", "bos", "eos", "pad"]

training_strategy:
  multi_task_learning:
    - task: "Text Image Translation (TIT)"
      weight: 1.2
      description: "Direct image to text translation"
    - task: "Machine Translation (MT)"
      weight: 0.5
      description: "Text-to-text translation for alignment"
    - task: "Optical Character Recognition (OCR)"
      weight: 0.5
      description: "Image to source text extraction"
      
  optimization:
    optimizer: "AdamW"
    learning_rate: 6e-4
    warmup_steps: 1000
    scheduler: "Cosine annealing"
    batch_size: 128
    gradient_accumulation: 32</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>DATASET_SPECIFICATION</h2>
                    <div class="structured-data">
                        <pre><code class="language-yaml">dataset_composition:
  real_data:
    size: 100000
    type: "Gujarati text images with English translations"
    sources:
      - handwritten_text
      - printed_text
      - various_fonts
      - real_world_conditions
    
  synthetic_data:
    generation_method: "Programmatic text rendering"
    variations:
      - background_textures: "Multiple"
      - font_families: "Diverse"
      - text_styles: "Various"
    purpose: "Improve generalization"
    
  ocr_augmentation:
    type: "OCR-extracted Gujarati text aligned with English"
    purpose: "Supervised training for auxiliary OCR task"
    alignment: "Text-level correspondence"

data_preprocessing:
  image_processing:
    resize: [224, 224]
    normalization: "CLIP preprocessing"
    augmentation: "Standard computer vision augmentations"
    
  text_processing:
    tokenization: "SentencePiece BPE"
    max_length: 1024
    padding: "Dynamic batching"
    special_token_handling: "Language direction indicators"</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>CODE_STRUCTURE</h2>
                    <div class="structured-data">
                        <pre><code class="language-yaml">repository_structure:
  root_files:
    - train.py: "Main training script with multi-task learning"
    - validate.py: "Model validation and evaluation"
    - README.md: "Project documentation"
    
  model/:
    - transformer.py: "Main Transformer model implementation"
    - config.py: "Model configuration dataclass"
    - encoder.py: "Text encoder implementation"
    - decoder.py: "Text decoder with cross-attention"
    - visionenc.py: "Vision encoder wrapper for CLIP"
    - STN.py: "Spatial Transformer Network (if used)"
    
  dataset/:
    - dataset.py: "Text-to-text translation dataset"
    - dataset_image.py: "Image translation datasets (TIT, OCR, validation)"
    - ocr_dataset.py: "OCR-specific dataset handling"
    - prepare_image.py: "Image preprocessing utilities"
    - prepere_data.py: "Data preparation scripts"
    
  tokenizer/:
    - tokenizer.py: "SentencePiece tokenizer wrapper"
    
  learning/:
    - utils.py: "Training utilities"
    - tokenizer.py: "Tokenizer learning scripts"

key_classes:
  Transformer:
    file: "model/transformer.py"
    purpose: "Main model class combining vision and text processing"
    key_methods:
      - forward(): "Main forward pass"
      - encode(): "Text encoding"
      - decode(): "Text generation with cross-attention"
      
  Config:
    file: "model/config.py"
    purpose: "Centralized configuration management"
    parameters: "All model hyperparameters"
    
  Tokenizer:
    file: "tokenizer/tokenizer.py"
    purpose: "Text tokenization and vocabulary management"
    features: "BPE tokenization with special tokens"</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>USAGE_EXAMPLES</h2>
                    <div class="structured-data">
                        <pre><code class="language-python"># Training Example
from model.transformer import Transformer
from model.config import Config
from tokenizer.tokenizer import Tokenizer

# Initialize components
special_tokens = ["&lt;en|gu&gt;", "&lt;gu|en&gt;", "&lt;en&gt;", "&lt;gu&gt;"]
tokenizer = Tokenizer(special_tokens)
tokenizer.load("data/tokenizer.model")

config = Config(vocab_size=tokenizer.get_vocab_size())
model = Transformer(config)

# Multi-task training loop structure
for epoch in range(max_epochs):
    # Machine Translation Task
    for batch in mt_dataloader:
        src_text, tgt_text = batch
        output = model(tgt_text, src_text)
        mt_loss = criterion(output, tgt_text)
        
    # Text Image Translation Task  
    for batch in tit_dataloader:
        images, tgt_text = batch
        output = model(tgt_text, images, is_image=True)
        tit_loss = criterion(output, tgt_text)
        
    # OCR Task
    for batch in ocr_dataloader:
        images, src_text = batch
        output = model(src_text, images, is_image=True)
        ocr_loss = criterion(output, src_text)
        
    total_loss = mt_lambda * mt_loss + tit_lambda * tit_loss + ocr_lambda * ocr_loss</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>INFERENCE_PIPELINE</h2>
                    <div class="structured-data">
                        <pre><code class="language-python"># Inference Example
def translate_image(model, image, tokenizer, source_lang="gu", target_lang="en"):
    """
    Translate text in image from source to target language
    
    Args:
        model: Trained Transformer model
        image: PIL Image or tensor [3, 224, 224]
        tokenizer: Trained tokenizer
        source_lang: Source language code
        target_lang: Target language code
    
    Returns:
        translated_text: String translation
    """
    # Prepare language tokens
    lang_token = tokenizer.get_token_id(f"&lt;{target_lang}&gt;")
    bos_token = tokenizer.get_token_id("bos")
    eos_token = tokenizer.get_token_id("eos")
    
    # Initialize decoder input
    decoder_input = torch.tensor([[lang_token, bos_token]])
    
    # Generate translation
    with torch.no_grad():
        for _ in range(max_length):
            output = model(decoder_input, image, is_image=True)
            next_token = output[:, -1, :].argmax(dim=-1)
            
            if next_token == eos_token:
                break
                
            decoder_input = torch.cat([decoder_input, next_token.unsqueeze(0)], dim=1)
    
    # Decode to text
    generated_ids = decoder_input[0, 2:].tolist()  # Skip lang_token and bos
    translation = tokenizer.decode(generated_ids)
    
    return translation</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>PERFORMANCE_METRICS</h2>
                    <div class="structured-data">
                        <pre><code class="language-yaml">evaluation_metrics:
  translation_quality:
    - BLEU: "Bilingual Evaluation Understudy score"
    - METEOR: "Metric for Evaluation of Translation with Explicit ORdering"
    - ChrF: "Character n-gram F-score"
    - BERTScore: "Semantic similarity using BERT embeddings"
    
  model_efficiency:
    - parameters: "Total trainable parameters"
    - inference_time: "Time per image translation"
    - memory_usage: "GPU memory consumption"
    - throughput: "Images processed per second"
    
  training_metrics:
    - convergence_rate: "Training loss reduction over time"
    - validation_loss: "Held-out dataset performance"
    - multi_task_balance: "Loss contribution from each task"

expected_performance:
  model_size: "~100M parameters"
  inference_speed: "&lt;1 second per image on GPU"
  translation_quality: "Competitive with OCR+MT pipeline"
  resource_efficiency: "Suitable for low-resource deployment"</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>DEPLOYMENT_CONSIDERATIONS</h2>
                    <div class="structured-data">
                        <pre><code class="language-yaml">deployment_options:
  research_environment:
    requirements:
      - python: ">=3.8"
      - pytorch: ">=1.12"
      - transformers: ">=4.20"
      - sentencepiece: ">=0.1.96"
      - PIL: "Image processing"
      - wandb: "Experiment tracking"
    
    hardware:
      - gpu: "NVIDIA GPU with >=8GB VRAM recommended"
      - cpu: "Multi-core CPU for data loading"
      - storage: ">=50GB for datasets and checkpoints"
      
  production_deployment:
    optimization:
      - model_quantization: "INT8 quantization for efficiency"
      - onnx_conversion: "ONNX format for cross-platform deployment"
      - tensorrt_optimization: "NVIDIA TensorRT for GPU inference"
      
    api_integration:
      - rest_api: "HTTP API for image upload and translation"
      - batch_processing: "Batch inference for multiple images"
      - caching: "Result caching for repeated queries"
      
  limitations:
    - language_support: "Currently limited to Gujarati-English"
    - image_quality: "Performance degrades with very low quality images"
    - text_density: "Optimized for moderate text density in images"
    - domain_specificity: "Best performance on training domain data"</code></pre>
                    </div>
                </section>

                <div class="doc-footer">
                    <p><strong>Repository:</strong> <a href="https://github.com/patelchaitany/Guj_eng" target="_blank">https://github.com/patelchaitany/Guj_eng</a></p>
                    <p><strong>Last Updated:</strong> <span id="last-updated"></span></p>
                    <p><strong>Documentation Format:</strong> Structured for LLM consumption and understanding</p>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="scripts/main.js"></script>
    <script>
        document.getElementById('last-updated').textContent = new Date().toLocaleDateString();
    </script>
</body>
</html>
