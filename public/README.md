# Guj-Eng OCR-Free Image Translation - Website

This is the documentation website for the End-to-End OCR-Free Image Translation project for Gujarati-English language pair.

## 🌐 Live Website

Visit the live website at: [Your Vercel URL will be here]

## 📁 Website Structure

- `index.html` - Main homepage with project overview
- `llm-docs.html` - LLM-friendly structured documentation
- `technical-docs.html` - Comprehensive technical documentation
- `styles/` - CSS stylesheets
- `scripts/` - JavaScript files

## 🚀 Local Development

To run the website locally:

```bash
# Using Python
python -m http.server 8000 --directory docs

# Using Node.js (if you have it)
npx serve docs

# Using PHP (if you have it)
php -S localhost:8000 -t docs
```

Then open `http://localhost:8000` in your browser.

## 📖 Documentation Pages

### Home Page (`index.html`)
- Project overview and key features
- Model architecture visualization
- Dataset information
- Research foundation
- Direct GitHub repository link

### LLM Documentation (`llm-docs.html`)
- Structured YAML format for LLM consumption
- Technical specifications
- Code examples and usage patterns
- Deployment considerations

### Technical Documentation (`technical-docs.html`)
- Getting started guide
- Installation instructions
- Model architecture details
- Training process
- Usage examples
- Configuration options

## 🎨 Features

- **Responsive Design**: Works on all devices
- **Modern UI**: Clean, professional interface
- **Code Highlighting**: Syntax highlighting for all code blocks
- **Interactive Navigation**: Smooth scrolling and mobile menu
- **Performance Optimized**: Fast loading and efficient caching
- **SEO Friendly**: Proper meta tags and structure

## 🔧 Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript**: Interactive functionality
- **Prism.js**: Code syntax highlighting
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter, JetBrains Mono)

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## 🤝 Contributing

This website is part of the main Guj-Eng project. To contribute:

1. Fork the main repository
2. Make your changes to the `docs/` directory
3. Test locally
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the main repository for details.

## 🔗 Links

- **Main Repository**: https://github.com/patelchaitany/Guj_eng
- **Research Paper**: https://arxiv.org/abs/2210.03887
- **Issues**: https://github.com/patelchaitany/Guj_eng/issues
