/* Documentation Specific Styles */

.docs-main {
    padding-top: 90px;
    min-height: 100vh;
    background: #f8fafc;
}

.docs-header {
    text-align: center;
    padding: 60px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 0;
}

.docs-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.docs-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.docs-content {
    max-width: 1000px;
    margin: 0 auto;
    padding: 60px 20px;
}

.doc-section {
    background: white;
    border-radius: 12px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.doc-section h2 {
    color: #2563eb;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
    font-family: 'JetBrains Mono', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.structured-data {
    background: #1e293b;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.structured-data pre {
    margin: 0;
    padding: 30px;
    background: transparent;
    overflow-x: auto;
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
}

.structured-data code {
    background: transparent;
    color: #e2e8f0;
    font-family: 'JetBrains Mono', monospace;
}

/* Syntax highlighting for YAML */
.language-yaml .token.key {
    color: #60a5fa;
}

.language-yaml .token.string {
    color: #34d399;
}

.language-yaml .token.number {
    color: #fbbf24;
}

.language-yaml .token.boolean {
    color: #f87171;
}

.language-yaml .token.comment {
    color: #6b7280;
    font-style: italic;
}

/* Syntax highlighting for Python */
.language-python .token.keyword {
    color: #c084fc;
}

.language-python .token.string {
    color: #34d399;
}

.language-python .token.function {
    color: #60a5fa;
}

.language-python .token.class-name {
    color: #fbbf24;
}

.language-python .token.comment {
    color: #6b7280;
    font-style: italic;
}

.language-python .token.number {
    color: #fbbf24;
}

/* Copy button for code blocks */
.copy-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #6b7280;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.copy-button:hover {
    background: #4b5563;
}

/* Active navigation link */
.nav-link.active {
    color: #2563eb;
    background-color: rgba(37, 99, 235, 0.1);
}

/* Documentation footer */
.doc-footer {
    background: #f1f5f9;
    padding: 30px;
    border-radius: 12px;
    margin-top: 40px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.doc-footer p {
    margin-bottom: 10px;
    color: #64748b;
}

.doc-footer a {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
}

.doc-footer a:hover {
    text-decoration: underline;
}

/* Table of Contents (if added) */
.toc {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 40px;
    position: sticky;
    top: 90px;
}

.toc h3 {
    color: #1e293b;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.toc ul {
    list-style: none;
    padding: 0;
}

.toc li {
    margin-bottom: 8px;
}

.toc a {
    color: #64748b;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.toc a:hover {
    color: #2563eb;
}

/* Code inline */
code:not([class*="language-"]) {
    background: #f1f5f9;
    color: #2563eb;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.9em;
}

/* Responsive design for docs */
@media (max-width: 768px) {
    .docs-header h1 {
        font-size: 2rem;
    }
    
    .docs-header p {
        font-size: 1rem;
    }
    
    .doc-section {
        padding: 30px 20px;
        margin-bottom: 30px;
    }
    
    .doc-section h2 {
        font-size: 1.3rem;
    }
    
    .structured-data pre {
        padding: 20px;
        font-size: 0.8rem;
    }
    
    .copy-button {
        top: 10px;
        right: 10px;
        padding: 6px 10px;
        font-size: 0.7rem;
    }
}

/* Print styles */
@media print {
    .navbar,
    .copy-button,
    .doc-footer {
        display: none;
    }
    
    .docs-main {
        padding-top: 0;
    }
    
    .doc-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .structured-data {
        background: #f5f5f5;
    }
    
    .structured-data code {
        color: #333;
    }
}

/* Loading animation */
.docs-content {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Scroll indicators */
.section-indicator {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
}

.section-indicator .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #cbd5e1;
    margin: 8px 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.section-indicator .dot.active {
    background: #2563eb;
}

/* Enhanced code block styling */
.structured-data {
    position: relative;
    overflow: hidden;
}

.structured-data::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #2563eb, #7c3aed, #db2777);
}

/* Improved readability */
.doc-section p {
    line-height: 1.7;
    color: #374151;
    margin-bottom: 16px;
}

.doc-section ul {
    margin-bottom: 16px;
}

.doc-section li {
    margin-bottom: 8px;
    color: #374151;
}

/* Focus styles for accessibility */
.copy-button:focus,
.nav-link:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .docs-main {
        background: #0f172a;
    }
    
    .doc-section {
        background: #1e293b;
        border-color: #334155;
    }
    
    .doc-section h2 {
        color: #60a5fa;
        border-color: #334155;
    }
    
    .doc-section p,
    .doc-section li {
        color: #cbd5e1;
    }
    
    .doc-footer {
        background: #1e293b;
        border-color: #334155;
    }
    
    .doc-footer p {
        color: #94a3b8;
    }
}
