<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guj-Eng OCR-Free Image Translation</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><i class="fas fa-language"></i> Guj-Eng Translator</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#architecture" class="nav-link">Architecture</a>
                <a href="#dataset" class="nav-link">Dataset</a>
                <a href="llm-docs.html" class="nav-link">LLM Docs</a>
                <a href="technical-docs.html" class="nav-link">Technical</a>
                <a href="https://github.com/patelchaitany/Guj_eng" class="nav-link github-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </div>
            <div class="hamburger" id="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main>
        <section id="home" class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">End-to-End OCR-Free Image Translation</h1>
                    <p class="hero-subtitle">Gujarati ↔ English Translation Directly from Images</p>
                    <p class="hero-description">
                        Revolutionary approach to translate text in images without relying on OCR, 
                        specifically designed for low-resource languages like Gujarati using Vision Transformers.
                    </p>
                    <div class="hero-buttons">
                        <a href="https://github.com/patelchaitany/Guj_eng" class="btn btn-primary" target="_blank">
                            <i class="fab fa-github"></i> View on GitHub
                        </a>
                        <a href="#features" class="btn btn-secondary">Learn More</a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="demo-card">
                        <div class="demo-input">
                            <i class="fas fa-image"></i>
                            <span>Gujarati Text Image</span>
                        </div>
                        <div class="demo-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="demo-output">
                            <i class="fas fa-language"></i>
                            <span>English Translation</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="features" class="features">
            <div class="container">
                <h2 class="section-title">Key Highlights</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3>Low-Resource Translation</h3>
                        <p>Extended the model to Gujarati, a language with limited OCR and parallel resources, enabling translation in resource-constrained environments.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3>Vision Transformer Upgrade</h3>
                        <p>Replaced ResNet with Vision Transformer (ViT) for better visual-text alignment and improved image understanding capabilities.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3>Hybrid Training Data</h3>
                        <p>Combined 100k real OCR-English pairs with synthetic image-text data to improve model generalization and robustness.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-target"></i>
                        </div>
                        <h3>Direct Translation</h3>
                        <p>Enables direct translation from images without OCR preprocessing, reducing error propagation from traditional OCR+MT cascades.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3>Multi-Task Learning</h3>
                        <p>Incorporates auxiliary MT (text-to-text) and OCR tasks with shared encoder-decoder parameters for better alignment.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3>CLIP Integration</h3>
                        <p>Uses CLIP Vision Encoder for robust image feature extraction and better cross-modal understanding.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="architecture" class="architecture">
            <div class="container">
                <h2 class="section-title">Model Architecture</h2>
                <div class="architecture-content">
                    <div class="architecture-diagram">
                        <div class="component">
                            <h4><i class="fas fa-camera"></i> Image Input</h4>
                            <p>224x224 RGB Images</p>
                        </div>
                        <div class="arrow-down">↓</div>
                        <div class="component">
                            <h4><i class="fas fa-eye"></i> CLIP Vision Encoder</h4>
                            <p>Feature Extraction</p>
                        </div>
                        <div class="arrow-down">↓</div>
                        <div class="component">
                            <h4><i class="fas fa-brain"></i> Transformer Architecture</h4>
                            <p>Encoder-Decoder with Cross-Attention</p>
                        </div>
                        <div class="arrow-down">↓</div>
                        <div class="component">
                            <h4><i class="fas fa-language"></i> Text Output</h4>
                            <p>Translated Text</p>
                        </div>
                    </div>
                    <div class="architecture-details">
                        <h3>Technical Specifications</h3>
                        <ul>
                            <li><strong>Image Encoder:</strong> CLIP Vision Encoder for robust visual feature extraction</li>
                            <li><strong>Text Processing:</strong> Transformer-based sequence-to-sequence architecture</li>
                            <li><strong>Model Dimension:</strong> 768-dimensional embeddings</li>
                            <li><strong>Attention Heads:</strong> 8 multi-head attention mechanisms</li>
                            <li><strong>Layers:</strong> 6 encoder layers, 6 decoder layers</li>
                            <li><strong>Training Strategy:</strong> Multi-task learning with MT, OCR, and TIT tasks</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="dataset" class="dataset">
            <div class="container">
                <h2 class="section-title">Dataset Information</h2>
                <div class="dataset-grid">
                    <div class="dataset-card">
                        <h3><i class="fas fa-images"></i> Real Data</h3>
                        <p><strong>100,000</strong> Gujarati text images with corresponding English translations</p>
                        <ul>
                            <li>Handwritten and printed text</li>
                            <li>Various fonts and styles</li>
                            <li>Real-world image conditions</li>
                        </ul>
                    </div>
                    <div class="dataset-card">
                        <h3><i class="fas fa-magic"></i> Synthetic Data</h3>
                        <p>Generated text images with diverse backgrounds and fonts</p>
                        <ul>
                            <li>Multiple background textures</li>
                            <li>Various font families</li>
                            <li>Improved generalization</li>
                        </ul>
                    </div>
                    <div class="dataset-card">
                        <h3><i class="fas fa-text-width"></i> OCR Augmentation</h3>
                        <p>OCR-extracted Gujarati text aligned with English translations</p>
                        <ul>
                            <li>Supervised training data</li>
                            <li>Text-level alignment</li>
                            <li>Multi-task learning support</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="research">
            <div class="container">
                <h2 class="section-title">Research Foundation</h2>
                <div class="research-content">
                    <p>This project is based on the research paper:</p>
                    <div class="citation">
                        <h4>"Improving End-to-End Text Image Translation From the Auxiliary Text Translation Task"</h4>
                        <p><em>Ma, Cong and Zhang, Yaping and Tu, Mei and Han, Xu and Wu, Linghui and Zhao, Yang and Zhou, Yu</em></p>
                        <p>arXiv preprint arXiv:2210.03887, 2022</p>
                        <a href="https://arxiv.org/abs/2210.03887" target="_blank" class="btn btn-outline">
                            <i class="fas fa-external-link-alt"></i> Read Paper
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Project</h4>
                    <ul>
                        <li><a href="https://github.com/patelchaitany/Guj_eng">GitHub Repository</a></li>
                        <li><a href="llm-docs.html">LLM Documentation</a></li>
                        <li><a href="technical-docs.html">Technical Docs</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="https://arxiv.org/abs/2210.03887">Research Paper</a></li>
                        <li><a href="#dataset">Dataset Info</a></li>
                        <li><a href="#architecture">Architecture</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>For questions and contributions, please visit our GitHub repository.</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Guj-Eng OCR-Free Image Translation Project. Open Source under MIT License.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>
