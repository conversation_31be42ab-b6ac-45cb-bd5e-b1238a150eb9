/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #2563eb;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
}

.nav-link:hover {
    color: #2563eb;
    background-color: rgba(37, 99, 235, 0.1);
}

.github-link {
    background: #24292e;
    color: white !important;
    padding: 8px 16px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.github-link:hover {
    background: #1a1e22;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 20px;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 40px;
    opacity: 0.8;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: white;
    color: #2563eb;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #2563eb;
}

.btn-outline {
    background: transparent;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.btn-outline:hover {
    background: #2563eb;
    color: white;
}

/* Demo Card */
.demo-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.demo-input, .demo-output {
    text-align: center;
    flex: 1;
}

.demo-input i, .demo-output i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.demo-arrow {
    font-size: 1.5rem;
    margin: 0 20px;
    opacity: 0.8;
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 60px;
    color: #1e293b;
}

.features {
    padding: 100px 0;
    background: #f8fafc;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1e293b;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Architecture Section */
.architecture {
    padding: 100px 0;
}

.architecture-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.architecture-diagram {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.component {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
    width: 100%;
    max-width: 300px;
    border: 2px solid #e2e8f0;
}

.component h4 {
    color: #2563eb;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.component p {
    color: #64748b;
    font-size: 0.9rem;
}

.arrow-down {
    font-size: 1.5rem;
    color: #2563eb;
    font-weight: bold;
}

.architecture-details h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #1e293b;
}

.architecture-details ul {
    list-style: none;
}

.architecture-details li {
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
    color: #64748b;
}

.architecture-details li:last-child {
    border-bottom: none;
}

/* Dataset Section */
.dataset {
    padding: 100px 0;
    background: #f8fafc;
}

.dataset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.dataset-card {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dataset-card h3 {
    color: #2563eb;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.dataset-card p {
    margin-bottom: 20px;
    color: #64748b;
}

.dataset-card ul {
    list-style: none;
}

.dataset-card li {
    padding: 5px 0;
    color: #64748b;
    position: relative;
    padding-left: 20px;
}

.dataset-card li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

/* Research Section */
.research {
    padding: 100px 0;
}

.research-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.citation {
    background: #f8fafc;
    padding: 40px;
    border-radius: 16px;
    margin-top: 30px;
    border-left: 4px solid #2563eb;
}

.citation h4 {
    color: #1e293b;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.citation p {
    color: #64748b;
    margin-bottom: 10px;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    margin-bottom: 20px;
    color: #f1f5f9;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #f1f5f9;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 20px;
    text-align: center;
    color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .architecture-content {
        grid-template-columns: 1fr;
    }
    
    .demo-card {
        flex-direction: column;
        gap: 20px;
    }
    
    .demo-arrow {
        transform: rotate(90deg);
    }
}
