# Deployment Guide - Vercel

This guide will help you deploy the Guj-Eng Translator website to Vercel.

## 🚀 Quick Deployment Options

### Option 1: Deploy via Vercel Web Interface (Recommended)

1. **Go to Vercel**: Visit [vercel.com](https://vercel.com)
2. **Sign up/Login**: Create account or login with GitHub
3. **Import Project**: Click "New Project" → "Import Git Repository"
4. **Connect GitHub**: Authorize Vercel to access your GitHub account
5. **Select Repository**: Choose `patelchaitany/Guj_eng`
6. **Configure Project**:
   - Project Name: `guj-eng-translator`
   - Framework Preset: `Other`
   - Root Directory: `./` (leave as default)
   - Build Command: Leave empty (static site)
   - Output Directory: `docs`
   - Install Command: Leave empty
7. **Deploy**: Click "Deploy"

### Option 2: Deploy via Vercel CLI

```bash
# Install Vercel CLI globally
npm install -g vercel

# Login to Vercel
vercel login

# Deploy from project root
vercel

# Follow the prompts:
# - Set up and deploy? Yes
# - Which scope? (your account)
# - Link to existing project? No
# - Project name: guj-eng-translator
# - In which directory is your code located? ./
# - Want to override settings? Yes
# - Output directory: docs
# - Build command: (leave empty)
# - Development command: python -m http.server 8000 --directory docs

# For production deployment
vercel --prod
```

### Option 3: Deploy via GitHub Integration

1. **Push to GitHub**: Make sure your code is pushed to GitHub
2. **Connect Vercel**: Go to Vercel dashboard → "Import Project"
3. **Auto-deploy**: Vercel will automatically deploy on every push to main branch

## 📋 Pre-deployment Checklist

- [ ] All files are in the `docs/` directory
- [ ] `vercel.json` configuration is present
- [ ] `package.json` is configured
- [ ] All links are relative (no absolute paths)
- [ ] Images and assets are properly referenced
- [ ] Code is pushed to GitHub repository

## 🔧 Configuration Files

### vercel.json
```json
{
  "version": 2,
  "name": "guj-eng-translator",
  "builds": [
    {
      "src": "docs/**/*",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/",
      "dest": "/docs/index.html"
    }
  ]
}
```

### package.json
```json
{
  "name": "guj-eng-translator-website",
  "version": "1.0.0",
  "scripts": {
    "deploy": "vercel --prod"
  },
  "vercel": {
    "outputDirectory": "docs"
  }
}
```

## 🌐 Custom Domain (Optional)

After deployment, you can add a custom domain:

1. **Go to Project Settings**: In Vercel dashboard
2. **Domains Tab**: Click "Domains"
3. **Add Domain**: Enter your custom domain
4. **Configure DNS**: Follow Vercel's DNS instructions
5. **SSL**: Automatic HTTPS certificate

## 📊 Environment Variables

No environment variables needed for this static site.

## 🔄 Automatic Deployments

Once connected to GitHub:
- **Production**: Deploys from `main` branch
- **Preview**: Deploys from feature branches
- **Instant**: Deploys on every push

## 🐛 Troubleshooting

### Common Issues:

1. **404 Errors**: Check that files are in `docs/` directory
2. **CSS/JS Not Loading**: Verify relative paths in HTML
3. **Build Fails**: Ensure no build command is set (static site)
4. **Routing Issues**: Check `vercel.json` routes configuration

### Debug Steps:

```bash
# Test locally first
python -m http.server 8000 --directory docs

# Check Vercel logs
vercel logs [deployment-url]

# Redeploy
vercel --prod --force
```

## 📈 Performance Optimization

Vercel automatically provides:
- **Global CDN**: Fast worldwide delivery
- **Automatic Compression**: Gzip/Brotli compression
- **Image Optimization**: Automatic image optimization
- **Caching**: Intelligent caching headers
- **HTTP/2**: Modern protocol support

## 🔒 Security Headers

The `vercel.json` includes security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`

## 📱 Analytics (Optional)

Add Vercel Analytics:
1. Go to project settings
2. Enable "Analytics"
3. Add analytics script to HTML (if desired)

## 🎯 Next Steps After Deployment

1. **Test Website**: Visit the deployed URL and test all pages
2. **Update README**: Add the live URL to your main README
3. **Share**: Share the live website URL
4. **Monitor**: Check Vercel dashboard for performance metrics
5. **Custom Domain**: Consider adding a custom domain

## 📞 Support

- **Vercel Docs**: https://vercel.com/docs
- **Vercel Community**: https://github.com/vercel/vercel/discussions
- **Project Issues**: https://github.com/patelchaitany/Guj_eng/issues
